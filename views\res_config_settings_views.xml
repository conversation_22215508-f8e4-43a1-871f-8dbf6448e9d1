<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Adding fields in settings to enable dead stock and out of stock graphs. -->
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.inventory.stock.dashboard.odoo</field>
        <field name="model">res.config.settings</field>
        <field name="priority" eval="99"/>
        <field name="inherit_id" ref="stock.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <block id="production_lot_info" position="after">
                <div class="app_settings_block">
                    <h2>Dashboard</h2>
                    <div class="row mt16 o_settings_container">
                        <div class="col-12 col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="out_of_stock"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="out_of_stock"/>
                                <div class="text-muted">
                                    Set Your Out Of Stock Quantity
                                </div>
                                <field name="out_of_stock_quantity" invisible="out_of_stock == False"/>
                            </div>
                        </div>
                        <div class="col-12 col-lg-6 o_setting_box">
                            <div class="o_setting_left_pane">
                                <field name="dead_stock_bol"/>
                            </div>
                            <div class="o_setting_right_pane">
                                <label for="dead_stock_bol"/>
                                <div class="text-muted">
                                    Show Dead Stocks In Dashboard
                                </div>
                                <div>
                                    <field name="dead_stock" invisible="dead_stock_bol == False"/>
                                </div>
                                <div>
                                    <field name="dead_stock_type" invisible="dead_stock_bol == False"/>
                                    <span invisible="dead_stock_bol == False">
                                        &#160; Duration
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </block>
        </field>
    </record>
</odoo>
