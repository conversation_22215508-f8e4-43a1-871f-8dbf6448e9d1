.item-container {
	background-image: -webkit-linear-gradient(white, white);
}
.item-header {
	padding-left: 30px;
	 background-image: -webkit-linear-gradient(white, #c3c9d4);
}
.col-sm,
.col-sm-1,
.col-sm-10,
.col-sm-11,
.col-sm-12,
.col-sm-2,
.col-sm-3,
.col-sm-4,
.col-xl-auto {
    position: relative;
    padding-right: 7.5px;
    padding-left: 7.5px;
}
#location_table thead th {
  border-bottom: none;
  background-color: #67b7dc;
  color: white;
  width: 250px;
  height: 30px;
}
.accounts-dashboard-wrap .card-header {
    background-color:
        transparent;
    border-bottom: 1px solid rgba(0, 0, 0, .125);
    padding: .75rem 1.25rem;
    position: relative;
    border-top-left-radius: .25rem;
    border-top-right-radius: .25rem;
}
.accounts-dashboard-wrap .fa:hover {
    -ms-transform: scale(1.5);
    /* IE 9 */
    -webkit-transform: scale(1.5);
    /* Safari 3-8 */
    transform: scale(1.5);
}
.accounts-dashboard-wrap .card-header>.card-tools {
    float: right;
    margin-right: -.625rem;
}
.right {
    float: left;
}
.accounts-dashboard-wrap .tooltip:hover .tooltiptext {
    visibility: visible;
}
.accounts-dashboard-wrap .col-6 {
    -ms-flex: 0 0 50%;
    flex: 0 0 50%;
    max-width: 50%;
}
.accounts-dashboard-wrap .fa-cog {
    content: "\f013"
}
.accounts-dashboard-wrap .fa,
.accounts-dashboard-wrap .fas {
    font-weight: 900;
}
.accounts-dashboard-wrap .fa,
.accounts-dashboard-wrap .fab,
.accounts-dashboard-wrap .fad,
.accounts-dashboard-wrap .fal,
.accounts-dashboard-wrap .far,
.accounts-dashboard-wrap .fas {
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    display: inline-block;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    line-height: 1;
}
.accounts-dashboard-wrap .info-box .info-box-icon {

    border-radius: .25rem;
    -ms-flex-align: center;
    align-items: center;
    display: -ms-flexbox;
    display: flex;
    font-size: 1.875rem;
    -ms-flex-pack: center;
    justify-content: center;
    text-align: center;
    width: 70px;
}
.accounts-dashboard-wrap .info-box {
    box-shadow: 0 0 1px rgba(0, 0, 0, .125), 0 1px 3px rgba(0, 0, 0, .2);
    border-radius: .25rem;
    background: #fff;
    display: -ms-flexbox;
    display: flex;
    margin-bottom: 1rem;
    min-height: 80px;
    padding: .5rem;
    position: relative;
}
.accounts-dashboard-wrap .o_datepicker .o_datepicker_input {
    width: 100%;
    cursor: pointer;
}
.accounts-dashboard-wrap #overdue {
    width: 100%;
    cursor: pointer;
}
.accounts-dashboard-wrap .o_input {
    border: 1px solid #cfcfcf;
    border-top-style: none;
    border-right-style: none;
    border-left-style: none;
}
.accounts-dashboard-wrap .in_graph {
    padding-left: 90px;
    height: auto;
    padding-bottom: 65px;
    text-align: center !important;
}
.accounts-dashboard-wrap .oh_dashboards {
    padding-top: 15px;
    background-color: #f8faff !important;
}
.accounts-dashboard-wrap .container-fluid.o_in_dashboard {
    padding: 0px !important;
}
.accounts-dashboard-wrap .o_action_manager {
    overflow-y: scroll !important;
    max-width: 100%;
}
// new tile

.oh_dashboards {
    background-color: #ececec;
}
.accounts-dashboard-wrap .container {
    margin: 50px 0 0 100px;
}
.accounts-dashboard-wrap .o_dashboards {
    color: #2a2a2a;
    background-color: #f2f2f2 !important;
}
.accounts-dashboard-wrap .dash-header {
    margin: 15px 0px 12px 0 !important;
    display: block;
    padding: 7px 25px 7px 0;
    color: #0e1319;
    font-size: 2rem;
    font-weight: 400;
    background-color:
        rgba(255, 255, 255, 0.9) !important;
    color: #212529;
    padding: 1.5rem;
    border-radius: 3px;
    box-shadow: 0 0px 10px 0px rgba(0, 0, 0, 0.05) !important;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.accounts-dashboard-wrap .dashboard-h1 {
    display: block;
    padding: 7px 25px 7px 0;
    color: #0e1319;
    font-size: 2rem;
    font-weight: 400;
    color: #212529;
    float: left;
    margin-bottom: 0;
}
.accounts-dashboard-wrap .card {
    position: relative !important;
    border-top: 0 !important;
    margin-bottom: 30px !important;
    width: 100% !important;
    background-color: #ffffff !important;
    border-radius: 0.25rem !important;
    padding: 0px !important;
    -webkit-transition: .5s !important;
    transition: .5s !important;
    display: -ms-flexbox !important;
    display: flex !important;
    -ms-flex-direction: column !important;
    flex-direction: column !important;
    box-shadow: 0 0px 10px 0px rgba(0, 0, 0, 0.05) !important;
    border-radius: 0.25rem;
}
.accounts-dashboard-wrap .card-header {
    border: 0;
    padding: 0;
}
.accounts-dashboard-wrap .card-header>.card-tools {
    float: right;
    margin-right: 0.375rem;
    margin-top: 5px;
    margin-bottom: 10px;
}
.accounts-dashboard-wrap .card-header i.fa {
    font-size: 1.3rem;
    display: inline-block;
    padding: 0 0px;
    margin: 0 0px;
    color: #57769c;
    opacity: .8;
    -webkit-transition: 0.3s linear;
    transition: 0.3s linear;
}
.accounts-dashboard-wrap .dropdown-toggle::after {
    display: inline-block;
    margin-left: 0.255em;
    vertical-align: 0.255em;
    content: "";
    border-top: 0.3em solid;
    border-right: 0.3em solid transparent;
    border-bottom: 0;
    border-left: 0.3em solid transparent;
    color: #7891af;
}
.accounts-dashboard-wrap .account-details {
    display: flex;
}
.main-title {
    color: #a3a3a3;
    display: block;
    margin-bottom: 5px;
    font-size: 20px;
    font-weight: 400;
}
.accounts-dashboard-wrap .main-title {
    display: block;
    margin-bottom: 5px;
    font-size: 13px;
    font-weight: 600;
    color: #fff !important;
    text-transform: uppercase;
    padding: 1rem;
    border-radius: 5px;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}
.accounts-dashboard-wrap .card-body {
    background-color: rgba(255, 255, 255, 0.9) !important;
    color: #212529;
    padding-top: 0;
}
.accounts-dashboard-wrap .tile.wide.invoice {
    margin-bottom: 27px;
    -webkit-box-shadow: 1px 5px 24px 0 rgba(68, 102, 242, 0.05);
    box-shadow: 1px 5px 24px 0 rgba(68, 102, 242, 0);
    background-color: #ffffff;
    border-radius: 5px;
    position: relative;
    width: 100%;
    padding: 0rem 0rem;
    border: 1px solid rgba(0, 0, 0, 0.07);
    height: 140px;
}
.accounts-dashboard-wrap .box-1 .main-title {
    background: #67b7dc;
    color: #fff;
}
.accounts-dashboard-wrap .box-2 .main-title {
    background: #6794dc !important;
    color: #fff;
}
.accounts-dashboard-wrap .box-3 .main-title {
    background: #8067dc;
    color: #fff;
}
.accounts-dashboard-wrap .box-4 .main-title {
    background: #c767dc;
    color: #fff;
}
.accounts-dashboard-wrap .count {
    margin-bottom: 1rem;
}
.accounts-dashboard-wrap .main-title~div {
    display: flex;
    justify-content: space-between;
    margin-top: 1rem;
    padding: 1rem;
    background: #fff;
}
#location_table {
    background-color:  #fff;
    color: (--mauve);
}
#tile_main_div:hover {
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
  animation-name: example;
  animation-duration: 0.25s;
  border-left: 8px solid var(--mauve);
  box-shadow: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22);
}
#tiles:hover {
  border-top-left-radius: 10px;
  border-bottom-left-radius: 10px;
  animation-name: example;
  animation-duration: 0.25s;
  box-shadow: 0 14px 28px rgba(0,0,0,0.25), 0 10px 10px rgba(0,0,0,0.22);
}
.details_table{
align-content: left;
}
.graph_details_table{
 position: absolute;
 top: 45px;
 right: 15px;
 background-color: white;
 border-collapse: collapse;
 border: 1px solid #ddd;
}
.graph_details_table th{
  background-color:#67b7dc;
  color: white;
  width: 250px;
  height: 30px;
}
.graph_details_table td{
border: 1px solid #ddd;
height: 20px;
}
.graph_details_table tr:nth-child(even){background-color: #f2f2f2;}
.graph_details_table tr:hover {background-color: #ddd;}
#product_moves_selection{
    position: relative;
    right: 10px;
    top: 5px;
}
:root {
    /* Primary */
    --mauve: #7D7EAF;
    --pink-dark: #BD85BA;
    --pink: #F78EAD;
    --peach: #FFA48E;
    --orange: #FFCA71;
    --gold: #CEA716;
    --green: #1EC198;
    --grey: #a0a0a0;
    /* Light */
    --mauve-light: #e5e5ef;
    --pink-dark-light: #f2e7f1;
    --pink-light: #fde8ef;
    --peach-light: #ffede8;
    --orange-light: #fff4e3;
    --gold-light: #faf6e8;
    --green-light: #e9f9f5;
    --grey-light: #e0e0e0;

    /*Lighter*/
    --grey-lighter: #fafafa;
    --grey-dark-lighter: #f3f3f3;
}
/* Background */
.bg-mauve-light {
    background-color: var(--mauve-light);
}
.bg-pink-dark-light {
    background-color: var(--pink-dark-light);
}
.bg-pink-light {
    background-color: var(--pink-light);
}
.bg-peach-light {
    background-color: var(--peach-light);
}
.bg-orange-light {
    background-color: var(--orange-light);
}
.bg-gold-light {
    background-color: var(--gold-light);
}
.bg-green-light {
    background-color: var(--green-light);
}
/* Text */
.text-mauve {
    color: var(--mauve);
}
.text-pink-dark {
    color: var(--pink-dark);
}
.text-pink {
    color: var(--pink);
}
.text-peach {
    color: var(--peach);
}
.text-orange {
    color: var(--orange);
}
.text-gold {
    color: var(--gold);
}
.text-green {
    color: var(--green);
}
/* Cards */
/*.dashboard-card {
    border-radius: 0.3rem;
    display: flex;
    justify-content: center;
    padding: 1.7rem 1.5rem 1.5rem 1.5rem;
    margin: 1rem auto;
    height: 90px;
}*/
.dashboard-card__icon-container {
    height: 50px;
    width: 50px;
    border-radius: 50%;
}
.dashboard-card__icon-container i {
	font-size: 20px;
}
.dashboard-card__details {
    margin-left: 0rem !important;
    max-width: 120px;
}
.dashboard-card__details h3 {
    font-weight: 700;
    font-size: 1.5rem;
}
.dashboard-card__details h4 {
    font-weight: 700;
    font-size: 0.7rem;
    color: var(--grey);
    margin-top: -5px;
}
h2.section-header {
    font-weight: 700;
    font-size: 1.5rem;
}
.chart-container {
    border-radius: 0.3rem;
    padding: 1rem;
    margin: 1rem auto;
}
.chart-container.card-shadow {
    height: 100%;
}
.half_chart.chart-container.card-shadow {
	height: 49%;
}
.chart-container h2 {
    font-weight: 700;
    font-size: 1.125rem;
}
.item-container {
    background-color: var(--grey-lighter);
    border-radius: 0.3rem;
    padding: 1.2rem 1rem;
    margin: 1rem auto;
}
.item-container:hover {
    background-color: var(--grey-dark-lighter);
    transition: all 0.3s ease-in-out;
    cursor: pointer;
}
.count-container {
    font-weight: 700;
    font-size: 2rem;
    background-color: var(--mauve-light);
    color: var(--mauve);
    height: 60px !important;
    width: 60px !important;
    border-radius: 50%;
    display: -webkit-box;
    display: -webkit-flex;
    display: flex;
    justify-content: center;
    align-items: center;
}
.item-header {
    display: flex;
    align-items: flex-start;
}
.item-title h3 {
    font-size: 1.3rem;
    font-weight: 700;
}
.item-content ul {
    list-style: none;
    padding-left: 0px;
}
.item-content ul>li {
    font-size: 0.9rem;
    color: var(--grey);
    font-weight: 700;
}
/* Misc */
.card-shadow {
    -webkit-box-shadow: 1px 3px 5px 0px rgba(222, 222, 222, 1);
    -moz-box-shadow: 1px 3px 5px 0px rgba(222, 222, 222, 1);
    box-shadow: 1px 3px 5px 0px rgba(222, 222, 222, 1);
}
.table td,
.table th {
    border-top: 1px solid #eceff2;
}
.crm_scroll_table {
	max-height: 395px;
	overflow-y: auto;
}
.recent_activity_div .crm_scroll_table {
	max-height: 435px;
}
.crm_scroll_table thead {
    position: sticky;
    top: 0;
}
.dashboard-card__stat_late:hover {
     border-bottom-color: darkgray;
}
.dashboard-card__stat_waiting:hover {
     border-bottom-color: darkgray;
}
.dashboard-card__stat_backorder:hover {
     border-bottom-color: darkgray;
}

.location_value {
    font-weight: 700;
    font-size: 1.2rem;
  text-align: center;
}
.btn__custom-info{
    background-color: #e9ebf6;
    color: #070920;
    border-radius: 1.5rem;
    padding: 0.15rem 2rem;
    border: 1px solid #dddfea;
    position: absolute;
    right: 0px;
    top: -3px;
}
.btn__custom-info:hover{
    background-color: #dddfea;
    border: 1px solid #d2d4dd;
    transition: all 0.3s ease-in-out;
}
.btn_info {
  position: absolute;
    top: 18px;
    right: 17px;
    background-color: #e9ebf6;
    height: 28px;
    width: 26px;
    border-radius: 24%;
}
#top_product_selection {
  position: absolute;
  top: 18px;
}
#top_product_button {
    position: absolute;
    top: 1px;
    right: 63px;
}
#product_move_select {
    position: absolute;
    top: 1px;
    right: 63px;
}
#product_move_selection {
  position: absolute;
  top: 18px;
}
#stock_move_select {
    position: absolute;
    top: 1px;
    right: 63px;
}
#stock_moves_selection {
  position: absolute;
  top: 18px;
}
.location_table_value{
text-align: center;
}
/* X-Small devices (portrait phones, less than 576px)*/
@media (max-width: 575.98px) {
  #top_product_selection{
    width: 180%;
    margin-left: -100%;
    }
    #product_move_selection{
    width: 180%;
    margin-left: -100%;
    }
    #stock_move_selection{
    width: 180%;
    margin-left: -100%;
    }
}
.tile-container {
    padding: 1rem;
    display: flex;
    justify-content: space-around;
    align-items: center;
    flex-wrap: wrap;
    border-radius: 0.5rem;
    background-image: url('./assets/background.png');
    background-repeat: no-repeat;
    background-size: cover;
    background-position-x: right;
}
.tile-container:hover {
    opacity: 0.9;
    cursor: pointer;
    transition: all 0.4s ease-in-out;
}
.title-container__icon-container {
    padding: 1.3rem 1rem;
    border-radius: 0.5rem;
}
.title-container__icon {
    font-size: 1.5rem;
}
.title-container__count {
    font-size: 1.6rem;
    font-weight: 600;
}
.title-container__title {
    font-size: 1rem;
    font-weight: 600;
}
/* Colors */
.red-bkg {
    background-color: #FFE1E2;
}
.red-font {
    color: #E8565E;
}
.blue-bkg {
    background-color: #C2D5FF;
}
.blue-font {
    color: #225AE3;
}
.green-bkg {
    background-color: #BDE4E0;
}
.green-font {
    color: #2CA79A;
}
.pink-bkg {
    background-color: #FFE4EF;
}
.pink-font {
    color: #CE3372;
}
.yellow-bkg {
    background-color: #F9ECC6;
}
.yellow-font {
    color: #CBA846;
}
.white-bkg {
    background-color: #FFFFFF;
}
.white-font {
    color: #FFFFFF;
}
/*Second Section CSS */
.card-contianer__header {
    background-color: #37274B;
    padding: 0.4rem;
    border-top: 3px solid #D84315;
}
.card-container__content {
    padding: 1rem;
}
.card-container__normal-header {
    font-size: 1.2rem;
    color: #000000;
    font-weight: 600;
}
.card-container__header-text {
    font-size: 1.2rem;
    color: #FFFFFF;
    font-weight: 400;
    text-align: center;
}
.dashboard-card {
  box-shadow: rgba(60, 64, 67, 0.3) 0px 1px 2px 0px, rgba(60, 64, 67, 0.15) 0px 2px 6px 2px;
  padding: 1.5rem;
  margin: 0.5rem auto;
  min-height: 185px;
  display: flex !important;
  justify-content: space-between !important;
  align-items: flex-start !important;
}
.dashboard-card--border-top {
  border-top: 5px solid #000;
}
.dashboard-card--border-top-red {
  border-color: #D32F2F;
}
.dashboard-card--border-top-blue {
  border-color: #2962FF;
}
.dashboard-card--border-top-green {
  border-color: #00FF00;
}
.dashboard-card--border-top-purple {
  border-color: #800080;
}
.dashboard-card--border-top-brown {
  border-color: #964B00;
}
.dashboard-card--border-top-pink {
  border-color: #FFC0CB;
}
.dashboard-card--border-top-grey {
  border-color: #696969;
}
.dashboard-card--border-top-black {
  border-color: black;
}
.dashboard-card--border-top-rebecca {
  border-color: #663399;
}
.dashboard-card--border-top-steel {
  border-color: #607D8B;
}
.dashboard-card--border-top-orange {
  border-color: #FFA500;
}
.dashboard-card__title {
  font-weight: bold;
  display: block;
  margin-top: 0.5rem;
}
.dashboard-card__count {
  font-size: 3rem;
}
.dashboard-card__stats {
  list-style: none;
  padding-left: 0;
  width: 50%
}
.dashboard-card__stat_late {
  padding: 0.5rem 0rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.dashboard-card__stat_waiting {
  padding: 0.5rem 0rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.dashboard-card__stat_backorder {
  padding: 0.5rem 0rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.dashboard-card__stat-count_late {
  min-width: 25px;
  min-height: 20px;
  background-color: rgba(0, 0, 0, 0.1);
  font-size: 0.9rem;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
}
.dashboard-card__stat-count_waiting {
  min-width: 25px;
  min-height: 20px;
  background-color: rgba(0, 0, 0, 0.1);
  font-size: 0.9rem;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
}
.dashboard-card__stat-count_backorder {
  min-width: 25px;
  min-height: 20px;
  background-color: rgba(0, 0, 0, 0.1);
  font-size: 0.9rem;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.6);
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
}
.oh_dashboards {
font-family: 'Raleway', sans-serif;
}
.main-part{
width:100%;
margin:0 auto;
text-align: center;
padding: 0px 5px;
}
.cpanel{
width:32%;
display: inline-block;
background-color:#34495E;
color:#fff;
margin-top: 50px;
}
.icon-part i{
font-size: 30px;
padding:10px;
border:1px solid #fff;
border-radius:50%;
margin-top:-25px;
margin-bottom: 10px;
background-color:#34495E;
}
.icon-part p{
margin:0px;
font-size: 20px;
padding-bottom: 10px;
}
.card-content-part{
background-color: #2F4254;
padding: 5px 0px;
}
.cpanel .card-content-part:hover{
background-color: #5a5a5a;
cursor: pointer;
}
.card-content-part a{
color:#fff;
text-decoration: none;
}
.cpanel-green .icon-part,.cpanel-green .icon-part i{
background-color: #16A085;
}
.cpanel-green .card-content-part{
background-color: #149077;
}
.cpanel-orange .icon-part,.cpanel-orange .icon-part i{
background-color: #F39C12;
}
.cpanel-orange .card-content-part{
background-color: #DA8C10;
}
.cpanel-blue .icon-part,.cpanel-blue .icon-part i{
background-color: #2980B9;
}
.cpanel-blue .card-content-part{
background-color:#2573A6;
}
.cpanel-red .icon-part,.cpanel-red .icon-part i{
background-color:#E74C3C;
}
.cpanel-red .card-content-part{
background-color:#CF4436;
}
.cpanel-skyblue .icon-part,.cpanel-skyblue .icon-part i{
background-color:#8E44AD;
}
.cpanel-skyblue .card-content-part{
background-color:#803D9B;
}
.icon-part{
    height: 121px;
  background-color: #16A085;
  min-height: 120px;
}
.col-lg-3:hover {
  -ms-transform: scale(1);
    /* IE 9 */
    -webkit-transform: scale(1);
    /* Safari 3-8 */
    transform: scale(1.05);
}
option {
  color: black;
}
